#!/usr/bin/env python3
"""
Create placeholder TensorFlow Lite models for mobile deployment.
This script creates simple models that can be used for testing the ML inference pipeline.
"""

import os
import sys
import numpy as np
from pathlib import Path
import logging

try:
    import tensorflow as tf
    print(f"TensorFlow version: {tf.__version__}")
except ImportError:
    print("TensorFlow not found. Installing...")
    os.system("pip install tensorflow")
    import tensorflow as tf

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_autoencoder_model(input_dim=128, encoding_dim=64):
    """Create a simple autoencoder model."""
    logger.info(f"Creating autoencoder model (input_dim={input_dim}, encoding_dim={encoding_dim})")
    
    model = tf.keras.Sequential([
        tf.keras.layers.Input(shape=(input_dim,), name='input'),
        tf.keras.layers.Dense(96, activation='relu', name='encoder_1'),
        tf.keras.layers.Dense(encoding_dim, activation='relu', name='encoder_2'),
        tf.keras.layers.Dense(96, activation='relu', name='decoder_1'),
        tf.keras.layers.Dense(input_dim, activation='linear', name='output')
    ], name='behavioral_autoencoder')
    
    model.compile(optimizer='adam', loss='mse', metrics=['mae'])
    
    # Generate synthetic training data
    logger.info("Generating synthetic training data...")
    X_train = np.random.randn(1000, input_dim).astype(np.float32)
    
    # Add some structure to the data
    for i in range(input_dim // 4):
        X_train[:, i*4:(i+1)*4] = X_train[:, i*4:(i+1)*4] + np.random.randn(1000, 1)
    
    # Train the model briefly
    logger.info("Training autoencoder...")
    model.fit(X_train, X_train, epochs=5, batch_size=32, verbose=1, validation_split=0.2)
    
    return model

def create_classifier_model(input_dim=64, num_classes=2):
    """Create a simple binary classifier model."""
    logger.info(f"Creating classifier model (input_dim={input_dim}, num_classes={num_classes})")
    
    model = tf.keras.Sequential([
        tf.keras.layers.Input(shape=(input_dim,), name='input'),
        tf.keras.layers.Dense(32, activation='relu', name='hidden_1'),
        tf.keras.layers.Dropout(0.3, name='dropout_1'),
        tf.keras.layers.Dense(16, activation='relu', name='hidden_2'),
        tf.keras.layers.Dropout(0.2, name='dropout_2'),
        tf.keras.layers.Dense(1, activation='sigmoid', name='output')
    ], name='user_classifier')
    
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
    
    # Generate synthetic training data
    logger.info("Generating synthetic training data...")
    X_train = np.random.randn(1000, input_dim).astype(np.float32)
    
    # Create labels based on some pattern in the data
    y_train = (np.sum(X_train[:, :10], axis=1) > 0).astype(np.float32)
    
    # Train the model briefly
    logger.info("Training classifier...")
    model.fit(X_train, y_train, epochs=5, batch_size=32, verbose=1, validation_split=0.2)
    
    return model

def create_anomaly_detector_model(input_dim=64):
    """Create a simple anomaly detector model."""
    logger.info(f"Creating anomaly detector model (input_dim={input_dim})")
    
    model = tf.keras.Sequential([
        tf.keras.layers.Input(shape=(input_dim,), name='input'),
        tf.keras.layers.Dense(32, activation='relu', name='hidden_1'),
        tf.keras.layers.Dense(16, activation='relu', name='hidden_2'),
        tf.keras.layers.Dense(8, activation='relu', name='hidden_3'),
        tf.keras.layers.Dense(1, activation='sigmoid', name='output')
    ], name='anomaly_detector')
    
    model.compile(optimizer='adam', loss='mse', metrics=['mae'])
    
    # Generate synthetic training data
    logger.info("Generating synthetic training data...")
    X_train = np.random.randn(1000, input_dim).astype(np.float32)
    
    # Create anomaly scores (most data is normal, some is anomalous)
    y_train = np.random.beta(2, 8, 1000).astype(np.float32)  # Skewed towards 0 (normal)
    
    # Train the model briefly
    logger.info("Training anomaly detector...")
    model.fit(X_train, y_train, epochs=5, batch_size=32, verbose=1, validation_split=0.2)
    
    return model

def convert_to_tflite(model, output_path, model_name):
    """Convert a Keras model to TensorFlow Lite format."""
    logger.info(f"Converting {model_name} to TensorFlow Lite...")
    
    # Convert to TensorFlow Lite
    converter = tf.lite.TFLiteConverter.from_keras_model(model)
    
    # Apply optimizations
    converter.optimizations = [tf.lite.Optimize.DEFAULT]
    
    # Set supported types for better performance on mobile
    converter.target_spec.supported_types = [tf.float16]
    
    # Convert the model
    tflite_model = converter.convert()
    
    # Save the model
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'wb') as f:
        f.write(tflite_model)
    
    logger.info(f"Model saved to: {output_path}")
    
    # Print model info
    interpreter = tf.lite.Interpreter(model_content=tflite_model)
    interpreter.allocate_tensors()
    
    input_details = interpreter.get_input_details()
    output_details = interpreter.get_output_details()
    
    logger.info(f"Input shape: {input_details[0]['shape']}")
    logger.info(f"Output shape: {output_details[0]['shape']}")
    logger.info(f"Model size: {len(tflite_model)} bytes")
    
    return str(output_path)

def test_tflite_model(model_path, input_shape):
    """Test a TensorFlow Lite model with dummy data."""
    logger.info(f"Testing TensorFlow Lite model: {model_path}")
    
    # Load the TFLite model
    interpreter = tf.lite.Interpreter(model_path=model_path)
    interpreter.allocate_tensors()
    
    # Get input and output tensors
    input_details = interpreter.get_input_details()
    output_details = interpreter.get_output_details()
    
    # Create test input
    test_input = np.random.randn(*input_shape).astype(np.float32)
    
    # Set input tensor
    interpreter.set_tensor(input_details[0]['index'], test_input)
    
    # Run inference
    interpreter.invoke()
    
    # Get output
    output_data = interpreter.get_tensor(output_details[0]['index'])
    
    logger.info(f"Test input shape: {test_input.shape}")
    logger.info(f"Test output shape: {output_data.shape}")
    logger.info(f"Test output sample: {output_data.flatten()[:5]}")
    
    return True

def main():
    """Main function to create all placeholder models."""
    # Set output directory
    script_dir = Path(__file__).parent
    output_dir = script_dir / "../../mobile_app/assets/ml_models"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Creating placeholder models in: {output_dir}")
    
    try:
        # Create and convert autoencoder
        logger.info("=" * 50)
        autoencoder = create_autoencoder_model(input_dim=128, encoding_dim=64)
        autoencoder_path = convert_to_tflite(
            autoencoder, 
            output_dir / "behavioral_autoencoder.tflite",
            "autoencoder"
        )
        test_tflite_model(autoencoder_path, (1, 128))
        
        # Create and convert classifier
        logger.info("=" * 50)
        classifier = create_classifier_model(input_dim=128, num_classes=2)
        classifier_path = convert_to_tflite(
            classifier,
            output_dir / "user_classifier.tflite",
            "classifier"
        )
        test_tflite_model(classifier_path, (1, 128))
        
        # Create and convert anomaly detector
        logger.info("=" * 50)
        anomaly_detector = create_anomaly_detector_model(input_dim=128)
        anomaly_path = convert_to_tflite(
            anomaly_detector,
            output_dir / "anomaly_detector.tflite",
            "anomaly_detector"
        )
        test_tflite_model(anomaly_path, (1, 128))
        
        logger.info("=" * 50)
        logger.info("All models created successfully!")
        logger.info(f"Models saved in: {output_dir}")
        logger.info("Files created:")
        for model_file in output_dir.glob("*.tflite"):
            size_mb = model_file.stat().st_size / (1024 * 1024)
            logger.info(f"  - {model_file.name} ({size_mb:.2f} MB)")
        
    except Exception as e:
        logger.error(f"Error creating models: {e}")
        raise

if __name__ == "__main__":
    main()
