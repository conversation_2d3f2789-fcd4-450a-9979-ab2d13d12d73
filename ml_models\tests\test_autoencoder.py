"""
Tests for Behavioral Autoencoder
"""

import pytest
import numpy as np
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from models.autoencoder import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Autoencoder<PERSON>rainer, AutoencoderPredictor


class TestBehavioralAutoencoder:
    """Test cases for BehavioralAutoencoder"""
    
    def setup_method(self):
        """Setup test data"""
        self.input_dim = 20
        self.n_samples = 100
        
        # Generate synthetic behavioral data
        np.random.seed(42)
        self.X_normal = np.random.normal(0, 1, (self.n_samples, self.input_dim))
        self.X_anomaly = np.random.normal(3, 2, (20, self.input_dim))  # Anomalous data
        
        # Create autoencoder
        self.autoencoder = BehavioralAutoencoder(
            input_dim=self.input_dim,
            encoding_dims=[16, 8, 4],
            dropout_rate=0.1
        )
    
    def test_autoencoder_initialization(self):
        """Test autoencoder initialization"""
        assert self.autoencoder.input_dim == self.input_dim
        assert self.autoencoder.encoding_dims == [16, 8, 4]
        assert not self.autoencoder.is_trained
        assert self.autoencoder.model_name == "BehavioralAutoencoder"
    
    def test_autoencoder_build(self):
        """Test autoencoder architecture building"""
        # Build the model
        self.autoencoder.fit(self.X_normal)
        
        assert self.autoencoder.encoder is not None
        assert self.autoencoder.decoder is not None
        assert self.autoencoder.autoencoder is not None
        assert self.autoencoder.is_trained
    
    def test_autoencoder_prediction(self):
        """Test autoencoder prediction"""
        # Train the model
        self.autoencoder.fit(self.X_normal)
        
        # Test reconstruction
        reconstructions = self.autoencoder.predict(self.X_normal[:10])
        assert reconstructions.shape == (10, self.input_dim)
        
        # Test anomaly scores
        anomaly_scores = self.autoencoder.predict_proba(self.X_normal[:10])
        assert anomaly_scores.shape == (10,)
        assert np.all(anomaly_scores >= 0)
        assert np.all(anomaly_scores <= 1)
    
    def test_reconstruction_errors(self):
        """Test reconstruction error calculation"""
        self.autoencoder.fit(self.X_normal)
        
        errors = self.autoencoder.get_reconstruction_errors(self.X_normal[:10])
        assert errors.shape == (10,)
        assert np.all(errors >= 0)
    
    def test_latent_representation(self):
        """Test latent representation extraction"""
        self.autoencoder.fit(self.X_normal)
        
        latent = self.autoencoder.get_latent_representation(self.X_normal[:10])
        assert latent.shape == (10, 4)  # Last encoding dimension
    
    def test_threshold_setting(self):
        """Test reconstruction threshold setting"""
        self.autoencoder.fit(self.X_normal)
        
        threshold = 0.5
        self.autoencoder.set_reconstruction_threshold(threshold)
        assert self.autoencoder.reconstruction_threshold == threshold


class TestAutoencoderTrainer:
    """Test cases for AutoencoderTrainer"""
    
    def setup_method(self):
        """Setup test data"""
        self.input_dim = 15
        self.n_samples = 50
        
        np.random.seed(42)
        self.X_train = np.random.normal(0, 1, (self.n_samples, self.input_dim))
        self.X_val = np.random.normal(0, 1, (20, self.input_dim))
        
        self.autoencoder = BehavioralAutoencoder(
            input_dim=self.input_dim,
            encoding_dims=[12, 6, 3]
        )
        self.trainer = AutoencoderTrainer(self.autoencoder)
    
    def test_trainer_initialization(self):
        """Test trainer initialization"""
        assert self.trainer.model == self.autoencoder
        assert len(self.trainer.training_history) == 0
    
    def test_data_validation(self):
        """Test data validation"""
        # Valid data should pass
        assert self.trainer.validate_data(self.X_train)
        
        # Empty data should fail
        with pytest.raises(ValueError):
            self.trainer.validate_data(np.array([]))
        
        # Data with NaN should fail
        X_nan = self.X_train.copy()
        X_nan[0, 0] = np.nan
        with pytest.raises(ValueError):
            self.trainer.validate_data(X_nan)
    
    def test_data_preprocessing(self):
        """Test data preprocessing"""
        X_processed = self.trainer.preprocess_data(self.X_train)
        
        # Check shape is preserved
        assert X_processed.shape == self.X_train.shape
        
        # Check normalization (values should be in [0, 1] range approximately)
        assert np.all(X_processed >= 0)
        assert np.all(X_processed <= 1)
    
    def test_training(self):
        """Test model training"""
        # Train with minimal epochs for testing
        result = self.trainer.train(
            self.X_train,
            X_val=self.X_val,
            epochs=2,
            batch_size=16,
            verbose=0
        )
        
        # Check training results
        assert 'history' in result
        assert 'reconstruction_threshold' in result
        assert 'training_metadata' in result
        
        # Check model is trained
        assert self.autoencoder.is_trained
        assert self.autoencoder.reconstruction_threshold is not None
        
        # Check training history is logged
        assert len(self.trainer.training_history) > 0


class TestAutoencoderPredictor:
    """Test cases for AutoencoderPredictor"""
    
    def setup_method(self):
        """Setup test data"""
        self.input_dim = 10
        self.n_samples = 30
        
        np.random.seed(42)
        self.X_normal = np.random.normal(0, 1, (self.n_samples, self.input_dim))
        self.X_test = np.random.normal(0, 1, (10, self.input_dim))
        
        # Train a simple autoencoder
        self.autoencoder = BehavioralAutoencoder(
            input_dim=self.input_dim,
            encoding_dims=[8, 4]
        )
        trainer = AutoencoderTrainer(self.autoencoder)
        trainer.train(self.X_normal, epochs=2, verbose=0)
        
        self.predictor = AutoencoderPredictor(self.autoencoder)
    
    def test_predictor_initialization(self):
        """Test predictor initialization"""
        assert self.predictor.model == self.autoencoder
        
        # Should fail with untrained model
        untrained_model = BehavioralAutoencoder(input_dim=10)
        with pytest.raises(ValueError):
            AutoencoderPredictor(untrained_model)
    
    def test_risk_score_prediction(self):
        """Test risk score prediction"""
        risk_scores = self.predictor.predict_risk_score(self.X_test)
        
        assert risk_scores.shape == (10,)
        assert np.all(risk_scores >= 0)
        assert np.all(risk_scores <= 1)
    
    def test_anomaly_detection(self):
        """Test anomaly detection"""
        anomalies = self.predictor.predict_anomaly(self.X_test, threshold=0.5)
        
        assert anomalies.shape == (10,)
        assert np.all((anomalies == 0) | (anomalies == 1))
    
    def test_batch_prediction(self):
        """Test batch prediction"""
        risk_scores = self.predictor.predict_batch(self.X_test, batch_size=5)
        
        assert risk_scores.shape == (10,)
        assert np.all(risk_scores >= 0)
        assert np.all(risk_scores <= 1)
    
    def test_prediction_explanation(self):
        """Test prediction explanation"""
        feature_names = [f'feature_{i}' for i in range(self.input_dim)]
        explanation = self.predictor.explain_prediction(self.X_test[:3], feature_names)
        
        assert 'risk_scores' in explanation
        assert 'mean_risk' in explanation
        assert 'feature_names' in explanation
        assert 'top_anomalous_features' in explanation
        assert len(explanation['risk_scores']) == 3
    
    def test_confidence_prediction(self):
        """Test prediction with confidence"""
        risk_scores, confidence = self.predictor.predict_with_confidence(self.X_test)
        
        assert risk_scores.shape == (10,)
        assert confidence.shape == (10,)
        assert np.all(confidence >= 0)
        assert np.all(confidence <= 1)
    
    def test_batch_anomaly_detection(self):
        """Test batch anomaly detection"""
        results = self.predictor.detect_anomalies_batch(
            self.X_test, 
            threshold=0.5, 
            return_scores=True
        )
        
        assert 'anomaly_count' in results
        assert 'anomaly_rate' in results
        assert 'anomaly_indices' in results
        assert 'risk_scores' in results
        assert 'anomaly_labels' in results
        
        assert len(results['risk_scores']) == 10
        assert len(results['anomaly_labels']) == 10


if __name__ == "__main__":
    # Run basic tests
    print("Testing Behavioral Autoencoder...")
    
    # Test basic functionality
    input_dim = 10
    X = np.random.normal(0, 1, (50, input_dim))
    
    # Create and train autoencoder
    autoencoder = BehavioralAutoencoder(input_dim=input_dim)
    trainer = AutoencoderTrainer(autoencoder)
    
    print("Training autoencoder...")
    result = trainer.train(X, epochs=5, verbose=1)
    print(f"Training completed. Threshold: {result['reconstruction_threshold']:.4f}")
    
    # Test prediction
    predictor = AutoencoderPredictor(autoencoder)
    risk_scores = predictor.predict_risk_score(X[:10])
    print(f"Risk scores: {risk_scores}")
    
    print("All tests passed!")
