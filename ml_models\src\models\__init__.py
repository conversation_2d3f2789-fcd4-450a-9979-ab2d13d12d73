"""
ML Models for TrustChain-Auth Behavioral Biometrics
"""

from .base_model import (
    BaseBehavioralModel,
    ModelTrainer,
    ModelPredictor
)

from .autoencoder import (
    BehavioralAutoencoder,
    AutoencoderTrainer,
    AutoencoderPredictor
)

# TODO: Import other models when implemented
# from .one_class_svm import (
#     BehavioralOneClassSVM,
#     OneClassSVMTrainer,
#     OneClassSVMPredictor
# )

# from .contrastive_learning import (
#     ContrastiveLearningModel,
#     ContrastiveTrainer,
#     ContrastivePredictor
# )

# from .ensemble import (
#     BehavioralEnsemble,
#     EnsemblePredictor
# )

__all__ = [
    'BaseBehavioralModel',
    'ModelTrainer',
    'ModelPredictor',
    'BehavioralAutoencoder',
    'AutoencoderTrainer',
    'AutoencoderPredictor'
]
