"""
Autoencoder Model for Behavioral Biometrics Anomaly Detection
Uses deep autoencoders to detect anomalous behavioral patterns
"""

import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, Model
from typing import Dict, Any, List, Tuple, Optional
import logging

from .base_model import BaseBehavioralModel, ModelTrainer, ModelPredictor

logger = logging.getLogger(__name__)


class BehavioralAutoencoder(BaseBehavioralModel):
    """Autoencoder for behavioral biometrics anomaly detection"""
    
    def __init__(self, 
                 input_dim: int,
                 encoding_dims: List[int] = [64, 32, 16],
                 activation: str = 'relu',
                 dropout_rate: float = 0.2,
                 l2_reg: float = 1e-4,
                 model_name: str = "BehavioralAutoencoder",
                 model_version: str = "1.0"):
        
        super().__init__(model_name, model_version)
        
        self.input_dim = input_dim
        self.encoding_dims = encoding_dims
        self.activation = activation
        self.dropout_rate = dropout_rate
        self.l2_reg = l2_reg
        
        self.encoder = None
        self.decoder = None
        self.autoencoder = None
        self.reconstruction_threshold = None
        
        self.model_params = {
            'input_dim': input_dim,
            'encoding_dims': encoding_dims,
            'activation': activation,
            'dropout_rate': dropout_rate,
            'l2_reg': l2_reg
        }
    
    def _build_encoder(self) -> Model:
        """Build the encoder part of the autoencoder"""
        inputs = keras.Input(shape=(self.input_dim,), name='encoder_input')
        x = inputs
        
        # Add encoding layers
        for i, dim in enumerate(self.encoding_dims):
            x = layers.Dense(
                dim,
                activation=self.activation,
                kernel_regularizer=keras.regularizers.l2(self.l2_reg),
                name=f'encoder_dense_{i+1}'
            )(x)
            x = layers.Dropout(self.dropout_rate, name=f'encoder_dropout_{i+1}')(x)
        
        # Latent representation
        latent = layers.Dense(
            self.encoding_dims[-1],
            activation=self.activation,
            name='latent_representation'
        )(x)
        
        encoder = Model(inputs, latent, name='encoder')
        return encoder
    
    def _build_decoder(self) -> Model:
        """Build the decoder part of the autoencoder"""
        latent_inputs = keras.Input(shape=(self.encoding_dims[-1],), name='decoder_input')
        x = latent_inputs
        
        # Add decoding layers (reverse of encoding)
        decoding_dims = self.encoding_dims[:-1][::-1]  # Reverse and exclude last
        
        for i, dim in enumerate(decoding_dims):
            x = layers.Dense(
                dim,
                activation=self.activation,
                kernel_regularizer=keras.regularizers.l2(self.l2_reg),
                name=f'decoder_dense_{i+1}'
            )(x)
            x = layers.Dropout(self.dropout_rate, name=f'decoder_dropout_{i+1}')(x)
        
        # Output layer (reconstruction)
        outputs = layers.Dense(
            self.input_dim,
            activation='linear',  # Linear for continuous features
            name='decoder_output'
        )(x)
        
        decoder = Model(latent_inputs, outputs, name='decoder')
        return decoder
    
    def _build_autoencoder(self) -> Model:
        """Build the complete autoencoder"""
        inputs = keras.Input(shape=(self.input_dim,), name='autoencoder_input')
        
        # Encode
        encoded = self.encoder(inputs)
        
        # Decode
        decoded = self.decoder(encoded)
        
        autoencoder = Model(inputs, decoded, name='autoencoder')
        return autoencoder
    
    def fit(self, X: np.ndarray, y: Optional[np.ndarray] = None) -> 'BehavioralAutoencoder':
        """Build the autoencoder architecture (actual training happens in trainer)"""
        # Build the model architecture
        self.encoder = self._build_encoder()
        self.decoder = self._build_decoder()
        self.autoencoder = self._build_autoencoder()

        # Compile the model
        self.autoencoder.compile(
            optimizer='adam',
            loss='mse',
            metrics=['mae']
        )

        # Store feature names if provided
        if hasattr(X, 'columns'):
            self.feature_names = list(X.columns)

        self.is_trained = True
        return self
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Predict reconstructions"""
        if not self.is_trained:
            raise ValueError("Model must be trained before prediction")
        
        return self.autoencoder.predict(X)
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """Predict reconstruction errors as anomaly scores"""
        if not self.is_trained:
            raise ValueError("Model must be trained before prediction")
        
        # Get reconstructions
        reconstructions = self.autoencoder.predict(X)
        
        # Calculate reconstruction errors
        reconstruction_errors = np.mean(np.square(X - reconstructions), axis=1)
        
        # Convert to probabilities (higher error = higher anomaly probability)
        if self.reconstruction_threshold is not None:
            # Normalize by threshold
            anomaly_scores = reconstruction_errors / self.reconstruction_threshold
            # Clip to [0, 1] range
            anomaly_scores = np.clip(anomaly_scores, 0, 1)
        else:
            # Use min-max normalization
            min_error = np.min(reconstruction_errors)
            max_error = np.max(reconstruction_errors)
            if max_error > min_error:
                anomaly_scores = (reconstruction_errors - min_error) / (max_error - min_error)
            else:
                anomaly_scores = np.zeros_like(reconstruction_errors)
        
        return anomaly_scores
    
    def get_reconstruction_errors(self, X: np.ndarray) -> np.ndarray:
        """Get reconstruction errors for input data"""
        reconstructions = self.predict(X)
        return np.mean(np.square(X - reconstructions), axis=1)
    
    def set_reconstruction_threshold(self, threshold: float):
        """Set the reconstruction error threshold for anomaly detection"""
        self.reconstruction_threshold = threshold
    
    def get_latent_representation(self, X: np.ndarray) -> np.ndarray:
        """Get latent representation of input data"""
        if not self.is_trained:
            raise ValueError("Model must be trained before getting latent representation")
        
        return self.encoder.predict(X)
    
    def _get_model_data(self) -> Dict[str, Any]:
        """Get model-specific data for saving"""
        model_data = {
            'reconstruction_threshold': self.reconstruction_threshold
        }
        
        if self.autoencoder is not None:
            # Save model weights
            model_data['autoencoder_weights'] = self.autoencoder.get_weights()
            model_data['autoencoder_config'] = self.autoencoder.get_config()
        
        return model_data
    
    def _set_model_data(self, model_data: Dict[str, Any]):
        """Set model-specific data when loading"""
        self.reconstruction_threshold = model_data.get('reconstruction_threshold')
        
        if 'autoencoder_weights' in model_data and 'autoencoder_config' in model_data:
            # Rebuild the model architecture
            self.encoder = self._build_encoder()
            self.decoder = self._build_decoder()
            self.autoencoder = self._build_autoencoder()
            
            # Compile the model
            self.autoencoder.compile(
                optimizer='adam',
                loss='mse',
                metrics=['mae']
            )
            
            # Set the weights
            self.autoencoder.set_weights(model_data['autoencoder_weights'])


class AutoencoderTrainer(ModelTrainer):
    """Trainer for behavioral autoencoder"""

    def __init__(self, model: BehavioralAutoencoder):
        super().__init__(model)
        self.early_stopping = None
        self.model_checkpoint = None

    def train(self,
              X_train: np.ndarray,
              y_train: Optional[np.ndarray] = None,
              X_val: Optional[np.ndarray] = None,
              y_val: Optional[np.ndarray] = None,
              epochs: int = 100,
              batch_size: int = 32,
              validation_split: float = 0.2,
              early_stopping_patience: int = 10,
              reduce_lr_patience: int = 5,
              verbose: int = 1,
              **kwargs) -> Dict[str, Any]:
        """Train the autoencoder"""

        # Validate data
        self.validate_data(X_train)

        # Build the model if not already built
        if self.model.autoencoder is None:
            self.model.fit(X_train)

        # Preprocess data
        X_train_processed = self.preprocess_data(X_train)
        X_val_processed = None
        if X_val is not None:
            X_val_processed = self.preprocess_data(X_val)

        # Setup callbacks
        callbacks = self._setup_callbacks(
            early_stopping_patience,
            reduce_lr_patience
        )

        # Train the model
        if X_val_processed is not None:
            validation_data = (X_val_processed, X_val_processed)
            validation_split = None
        else:
            validation_data = None

        history = self.model.autoencoder.fit(
            X_train_processed, X_train_processed,  # Autoencoder learns to reconstruct input
            epochs=epochs,
            batch_size=batch_size,
            validation_split=validation_split,
            validation_data=validation_data,
            callbacks=callbacks,
            verbose=verbose,
            **kwargs
        )

        # Calculate reconstruction threshold on training data
        train_errors = self.model.get_reconstruction_errors(X_train_processed)
        threshold = np.percentile(train_errors, 95)  # 95th percentile as threshold
        self.model.set_reconstruction_threshold(threshold)

        # Store training metadata
        self.model.training_metadata = {
            'epochs_trained': len(history.history['loss']),
            'final_loss': float(history.history['loss'][-1]),
            'reconstruction_threshold': float(threshold),
            'training_samples': len(X_train),
            'validation_samples': len(X_val) if X_val is not None else 0
        }

        # Log training completion
        self.log_training_step({
            'step': 'training_complete',
            'epochs': len(history.history['loss']),
            'final_loss': float(history.history['loss'][-1]),
            'threshold': float(threshold)
        })

        return {
            'history': history.history,
            'reconstruction_threshold': threshold,
            'training_metadata': self.model.training_metadata
        }

    def _setup_callbacks(self, early_stopping_patience: int, reduce_lr_patience: int) -> List:
        """Setup training callbacks"""
        callbacks = []

        # Early stopping
        self.early_stopping = keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=early_stopping_patience,
            restore_best_weights=True,
            verbose=1
        )
        callbacks.append(self.early_stopping)

        # Reduce learning rate on plateau
        reduce_lr = keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=reduce_lr_patience,
            min_lr=1e-7,
            verbose=1
        )
        callbacks.append(reduce_lr)

        return callbacks

    def preprocess_data(self, X: np.ndarray) -> np.ndarray:
        """Preprocess data for autoencoder training"""
        # Normalize features to [0, 1] range
        X_processed = X.copy()

        # Handle missing values
        X_processed = np.nan_to_num(X_processed, nan=0.0, posinf=1.0, neginf=0.0)

        # Min-max normalization
        X_min = np.min(X_processed, axis=0)
        X_max = np.max(X_processed, axis=0)

        # Avoid division by zero
        X_range = X_max - X_min
        X_range[X_range == 0] = 1.0

        X_normalized = (X_processed - X_min) / X_range

        return X_normalized


class AutoencoderPredictor(ModelPredictor):
    """Predictor for behavioral autoencoder"""

    def __init__(self, model: BehavioralAutoencoder):
        super().__init__(model)

    def _convert_to_risk_scores(self, proba: np.ndarray) -> np.ndarray:
        """Convert reconstruction errors to risk scores"""
        # For autoencoders, proba already represents anomaly scores (reconstruction errors)
        return proba

    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Predict risk scores with confidence intervals"""
        risk_scores = self.predict_risk_score(X)

        # Calculate confidence based on reconstruction error variance
        reconstructions = self.model.predict(X)
        reconstruction_errors = np.mean(np.square(X - reconstructions), axis=1)

        # Use reconstruction error as confidence (lower error = higher confidence)
        max_error = np.max(reconstruction_errors)
        confidence = 1.0 - (reconstruction_errors / max_error) if max_error > 0 else np.ones_like(reconstruction_errors)

        return risk_scores, confidence

    def explain_prediction(self, X: np.ndarray, feature_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """Provide detailed explanation for autoencoder predictions"""
        risk_scores = self.predict_risk_score(X)
        reconstructions = self.model.predict(X)

        # Calculate feature-wise reconstruction errors
        feature_errors = np.square(X - reconstructions)

        explanation = {
            'risk_scores': risk_scores.tolist(),
            'mean_risk': float(np.mean(risk_scores)),
            'max_risk': float(np.max(risk_scores)),
            'min_risk': float(np.min(risk_scores)),
            'reconstruction_threshold': self.model.reconstruction_threshold,
            'feature_reconstruction_errors': feature_errors.tolist()
        }

        if feature_names:
            explanation['feature_names'] = feature_names

            # Identify most anomalous features
            mean_feature_errors = np.mean(feature_errors, axis=0)
            top_anomalous_indices = np.argsort(mean_feature_errors)[-5:]  # Top 5 anomalous features
            explanation['top_anomalous_features'] = [
                {
                    'feature': feature_names[i],
                    'error': float(mean_feature_errors[i])
                }
                for i in top_anomalous_indices[::-1]  # Reverse to get highest first
            ]

        return explanation

    def detect_anomalies_batch(self,
                             X: np.ndarray,
                             threshold: Optional[float] = None,
                             return_scores: bool = False) -> Dict[str, Any]:
        """Detect anomalies in batch with detailed results"""
        if threshold is None:
            threshold = 0.5

        risk_scores = self.predict_risk_score(X)
        anomalies = risk_scores > threshold

        results = {
            'anomaly_count': int(np.sum(anomalies)),
            'anomaly_rate': float(np.mean(anomalies)),
            'anomaly_indices': np.where(anomalies)[0].tolist(),
            'threshold_used': threshold
        }

        if return_scores:
            results['risk_scores'] = risk_scores.tolist()
            results['anomaly_labels'] = anomalies.tolist()

        return results
