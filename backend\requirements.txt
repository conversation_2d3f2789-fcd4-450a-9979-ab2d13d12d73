# FastAPI Backend Dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9  # PostgreSQL
asyncpg==0.29.0  # Async PostgreSQL

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# Monitoring & Logging
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2  # For testing

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Firebase (Optional)
firebase-admin==6.4.0

# Redis (for caching)
redis==5.0.1
aioredis==2.0.1
