# TrustChain-Auth Project Structure

## 📁 Directory Overview

```
Trust<PERSON>hain-Auth/
├── 📱 mobile_app/                 # Flutter mobile application
│   ├── lib/
│   │   ├── core/                  # Core utilities and constants
│   │   ├── features/              # Feature-based modules
│   │   │   ├── auth/              # Authentication features
│   │   │   ├── behavioral/        # Behavioral data collection
│   │   │   ├── banking/           # Banking UI and features
│   │   │   ├── privacy/           # Privacy dashboard
│   │   │   └── security/          # Security features
│   │   ├── models/                # Data models
│   │   ├── services/              # Business logic services
│   │   ├── widgets/               # Reusable UI components
│   │   └── main.dart              # App entry point
│   ├── android/                   # Android-specific code
│   ├── ios/                       # iOS-specific code
│   ├── test/                      # Unit and widget tests
│   └── pubspec.yaml               # Flutter dependencies
│
├── 🧠 ml_models/                  # Machine Learning components
│   ├── src/                       # Source code for ML models
│   │   ├── data_collection/       # Data collection utilities
│   │   ├── preprocessing/         # Data preprocessing
│   │   ├── models/                # ML model implementations
│   │   │   ├── autoencoder.py     # Autoencoder for anomaly detection
│   │   │   ├── one_class_svm.py   # One-Class SVM implementation
│   │   │   └── contrastive.py     # Contrastive learning models
│   │   ├── training/              # Training pipelines
│   │   ├── evaluation/            # Model evaluation
│   │   └── deployment/            # TFLite conversion
│   ├── data/                      # Training and test datasets
│   │   ├── raw/                   # Raw behavioral data
│   │   ├── processed/             # Preprocessed data
│   │   └── synthetic/             # Synthetic data for testing
│   ├── models/                    # Trained model files
│   │   ├── tflite/                # TensorFlow Lite models
│   │   └── checkpoints/           # Training checkpoints
│   ├── notebooks/                 # Jupyter notebooks for experimentation
│   ├── tests/                     # ML model tests
│   └── scripts/                   # Utility scripts
│
├── 🔧 backend/                    # Optional backend services
│   ├── api/                       # FastAPI or Firebase functions
│   ├── auth/                      # Authentication services
│   └── monitoring/                # Logging and monitoring
│
├── 📚 docs/                       # Documentation
│   ├── architecture/              # System architecture docs
│   ├── api/                       # API documentation
│   └── user_guide/                # User manuals
│
├── ⚙️ config/                     # Configuration files
│   ├── development/               # Development environment config
│   └── production/                # Production environment config
│
├── 🧪 tests/                      # Integration and E2E tests
├── 📜 scripts/                    # Build and deployment scripts
├── 🐍 venv/                       # Python virtual environment
├── 📄 requirements.txt            # Python dependencies
├── 📋 PROJECT_STRUCTURE.md        # This file
├── 📖 README.md                   # Project overview
└── 📜 LICENSE                     # MIT License
```

## 🎯 Key Components

### Mobile App (Flutter)
- **Core**: Shared utilities, constants, and base classes
- **Features**: Modular feature implementation
- **Services**: Business logic and data management
- **Models**: Data structures and DTOs

### ML Models (Python)
- **Data Collection**: Behavioral data capture utilities
- **Models**: Autoencoder, One-Class SVM, Contrastive Learning
- **Training**: Automated training pipelines
- **Deployment**: TensorFlow Lite conversion

### Backend (Optional)
- **API**: RESTful services for logging and alerts
- **Auth**: Centralized authentication services
- **Monitoring**: System health and performance tracking

## 🚀 Getting Started

1. **Environment Setup**: Install Flutter SDK and Python dependencies
2. **Mobile App**: Navigate to `mobile_app/` and run `flutter create .`
3. **ML Models**: Activate virtual environment and install requirements
4. **Documentation**: Review architecture docs in `docs/`

## 📝 Development Workflow

1. **Feature Development**: Implement in respective feature modules
2. **Testing**: Write tests in corresponding test directories
3. **Documentation**: Update docs for new features
4. **Deployment**: Use scripts for automated deployment
